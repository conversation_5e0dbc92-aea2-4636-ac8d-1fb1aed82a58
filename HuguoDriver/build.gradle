buildscript {
    repositories {
        maven { url 'https://maven.fabric.io/public' }
    }

    dependencies {
        //classpath 'io.fabric.tools:gradle:1.+'
    }
}
repositories {
    mavenCentral()
    mavenLocal()
    maven { url 'https://maven.fabric.io/public' }
}
apply plugin: 'com.android.application'
//apply plugin: 'io.fabric'


allprojects {
    repositories {
        jcenter()
        maven {
            url "https://maven.google.com"
        }
    }
}
android {
    def versionPropsFile = file('version.properties')
    if (versionPropsFile.canRead()) {
        Properties versionProps = new Properties()
        versionProps.load(new FileInputStream(versionPropsFile))
        def versionMajor = versionProps['VERSION_MAJOR'].toInteger()
        def versionMinor = versionProps['VERSION_MINOR'].toInteger()
        def versionBuild = versionProps['VERSION_BUILD'].toInteger() + 1
        // Update the build number in the local file
        versionProps['VERSION_BUILD'] = versionBuild.toString()
        versionProps.store(versionPropsFile.newWriter(), null)
        defaultConfig {
            versionCode versionBuild
            versionName "${versionMajor}.${versionMinor}." + String.format("%05d", versionBuild)
        }
    }

    applicationVariants.all { variant ->
        variant.outputs.all {
            //outputFileName = "G:\\My Drive\\Public\\Huguo-2024-09-08\\" + outputFileName
        }
    }


    buildToolsVersion '35.0.0'
    aaptOptions.cruncherEnabled = false
    aaptOptions.useNewCruncher = false

    defaultConfig {
        applicationId "com.ondemandbay.huguodriver"
        useLibrary 'org.apache.http.legacy'

        minSdkVersion 26
        targetSdkVersion 35
        compileSdkVersion 35
        compileSdk 35
        multiDexEnabled true

        setProperty("archivesBaseName", "HuguoDriver-$versionName")

    }

    signingConfigs {
        release {
            storeFile file('../buildsystem/release.keystore')
            storePassword "m34gj9b4r3"
            keyAlias 'key0'
            keyPassword "m34gj9b4r3"
        }
    }


    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.txt'
            signingConfig signingConfigs.release
            debuggable false
        }
    }

    lintOptions {
        checkReleaseBuilds false
    }
//    dexOptions {
//        javaMaxHeapSize "4g" //specify the heap size for the dex process
//        preDexLibraries = false //delete the already predexed libraries
////        incremental true
//    }
    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/ASL2.0'
        exclude 'META-INF/androidx.localbroadcastmanager_localbroadcastmanager.version'
        exclude 'META-INF/androidx.swiperefreshlayout_swiperefreshlayout.version'
        exclude 'META-INF/androidx.print_print.version'
        exclude 'META-INF/androidx.customview_customview.version'
        exclude 'META-INF/androidx.cursoradapter_cursoradapter.version'
        exclude 'META-INF/androidx.drawerlayout_drawerlayout.version'
        exclude 'META-INF/androidx.versionedparcelable_versionedparcelable.version'
        exclude 'META-INF/androidx.interpolator_interpolator.version'
        exclude 'META-INF/androidx.fragment_fragment.version'
        exclude 'META-INF/androidx.vectordrawable_vectordrawable.version'
        exclude 'META-INF/androidx.core_core.version'
        exclude 'META-INF/androidx.legacy_legacy-support-core-ui.version'
        exclude 'META-INF/androidx.legacy_legacy-support-core-utils.version'
        exclude 'META-INF/proguard/androidx-annotations.pro'
        exclude 'META-INF/androidx.slidingpanelayout_slidingpanelayout.version'
        exclude 'META-INF/androidx.print_print.version'
        exclude 'META-INF/androidx.documentfile_documentfile.version'
        exclude 'META-INF/androidx.versionedparcelable_versionedparcelable.version'
        exclude 'META-INF/androidx.asynclayoutinflater_asynclayoutinflater.version'
        exclude 'META-INF/androidx.drawerlayout_drawerlayout.version'
        exclude 'META-INF/androidx.interpolator_interpolator.version'
        exclude 'META-INF/androidx.appcompat_appcompat.version'
        exclude 'META-INF/androidx.swiperefreshlayout_swiperefreshlayout.version'
        exclude 'META-INF/androidx.loader_loader.version'
        exclude 'META-INF/androidx.viewpager_viewpager.version'
        exclude 'META-INF/androidx.coordinatorlayout_coordinatorlayout.version'
        exclude 'META-INF/androidx.cursoradapter_cursoradapter.version'
        exclude 'META-INF/androidx.vectordrawable_vectordrawable-animated.version'
    }
    productFlavors {
    }
    namespace 'com.ondemandbay.huguodriver'
}
def acraVersion = '5.11.3'

dependencies {
    implementation project(':library_pinnlistivew')
    implementation 'com.android.support:multidex:1.0.0'
    implementation 'com.android.support:appcompat-v7:23.1.1'
    implementation 'com.soundcloud.android:android-crop:1.0.1@aar'
    implementation 'de.hdodenhof:circleimageview:2.0.0'
    //  compile 'com.facebook.android:facebook-android-sdk:4.+'
    //implementation 'com.sromku:simple-fb:4.1.1'
    implementation files('libs/android-query.0.26.7.jar')
    implementation files('libs/apache-mime4j-core-0.7.2.jar')
    implementation files('libs/httpclient-4.3.5.jar')
    implementation files('libs/httpcore-4.3.2.jar')
    implementation files('libs/httpmime-4.3.5.jar')
    implementation files('libs/splunk-mint-4.0.8.jar')
    implementation files('libs/gcm.jar')
    implementation 'com.android.support:support-vector-drawable:26.0.0-alpha1'
    implementation 'com.android.support:support-annotations:26.0.0-alpha1'
    implementation 'com.android.support:support-v13:26.0.0-alpha1'
    implementation 'com.android.support:appcompat-v7:26.0.0-alpha1'
    implementation 'com.android.support:gridlayout-v7:26.0.0-alpha1'
    implementation 'com.android.support:palette-v7:26.0.0-alpha1'
    implementation 'com.google.code.gson:gson:2.2.4'
    implementation 'joda-time:joda-time:2.3'
    implementation 'com.squareup:otto:1.3.5'
    implementation 'org.slf4j:slf4j-android:1.7.7'
    implementation 'com.nineoldandroids:library:2.4.0'
    //implementation 'com.google.guava:guava:16.0.1'
    implementation 'me.neavo:volley:2014.12.09'
    implementation 'com.squareup.okhttp3:okhttp:3.10.0'
    //add volley notwork class
    implementation 'com.google.firebase:firebase-core:16.0.0'
    implementation 'com.google.firebase:firebase-messaging:24.0.1'
    implementation 'com.google.android.gms:play-services-maps:19.0.0'
    implementation 'com.google.android.gms:play-services-plus:17.0.0'
    implementation 'com.google.android.gms:play-services:11.8.0'
    implementation("com.google.firebase:firebase-iid:21.1.0")
    implementation 'androidx.work:work-runtime-ktx:2.7.1'

    implementation 'com.google.android.gms:play-services-location:21.3.0'

    implementation('com.crashlytics.sdk.android:crashlytics:2.9.9@aar') {
        transitive = true;
    }
    implementation 'com.squareup.picasso:picasso:2.8'

//    implementation 'com.github.bumptech.glide:glide:4.11.0'
//    annotationProcessor 'com.github.bumptech.glide:compiler:4.11.0'

    implementation "ch.acra:acra-dialog:$acraVersion"
    implementation "ch.acra:acra-mail:$acraVersion"

    implementation("com.squareup.okhttp3:okhttp:4.12.0")
// define a BOM and its version
    implementation(platform("com.squareup.okhttp3:okhttp-bom:4.12.0"))

    // define any required OkHttp artifacts without version
    implementation("com.squareup.okhttp3:okhttp")
    implementation("com.squareup.okhttp3:logging-interceptor")

}
apply plugin: 'com.google.gms.google-services'
