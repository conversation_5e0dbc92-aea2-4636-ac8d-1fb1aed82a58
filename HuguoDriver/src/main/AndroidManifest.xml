<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:versionCode="10"
    android:versionName="1.4">



    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.STATUS_BAR_SERVICE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.CALL_PRIVILEGED" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_LOGS" />
    <uses-permission android:name="android.permission.WRITE_INTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />

    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<!--    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />-->


    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <permission
        android:name="com.ondemandbay.huguodriver.permission.C2D_MESSAGE"
        android:protectionLevel="signature" />

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:icon="@drawable/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:theme="@style/AppTheme" >

        <uses-library android:name="org.apache.http.legacy" android:required="false" />

        <activity
            android:name="com.ondemandbay.huguodriver.MapActivity"
            android:exported="true"

            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateHidden" >
        </activity>
        <activity
            android:name="com.ondemandbay.huguodriver.RegisterActivity"
            android:exported="true"

            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateHidden" >
        </activity>
        <activity
            android:name="com.ondemandbay.huguodriver.ProfileActivity"
            android:exported="true"

            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateHidden" >
        </activity>
        <activity
            android:name="com.ondemandbay.huguodriver.MenuDescActivity"
            android:exported="true"

            android:screenOrientation="portrait" >
        </activity>
        <activity
            android:name="com.ondemandbay.huguodriver.HistoryActivity"
            android:exported="true"

            android:screenOrientation="portrait" >
        </activity>
        <activity
            android:name="com.ondemandbay.huguodriver.HistoryDetailsActivity"
            android:exported="true"

            android:screenOrientation="portrait" >
        </activity>
        <activity
            android:name="com.ondemandbay.huguodriver.MainActivity"
            android:exported="true"

            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Light.NoTitleBar" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name="com.soundcloud.android.crop.CropImageActivity"
            android:exported="true"
            />

        <meta-data
            android:name="com.google.android.maps.v2.API_KEY"
            android:value="@string/map_api_key" />
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

<!--        &lt;!&ndash; facebook &ndash;&gt;-->
<!--        <activity android:name="com.facebook.FacebookActivity"-->
<!--            android:exported="true"-->

<!--            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"-->
<!--        android:theme="@android:style/Theme.Translucent.NoTitleBar"-->
<!--            tools:replace="android:theme"-->
<!--        android:label="@string/app_name" />-->

        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/app_id" />

        <!-- GCM -->

        <service android:name="com.ondemandbay.huguodriver.locationupdate.LocationUpdateService"
            android:foregroundServiceType="location"
            ></service>
        <service android:name="com.ondemandbay.huguodriver.YourService"/>
        <service android:name="com.ondemandbay.huguodriver.BoundService"/>



        <receiver android:name="com.ondemandbay.huguodriver.MyReceiver" />

        <service android:name="com.ondemandbay.huguodriver.gcm.NotificationService"/>

        <!--<service
            android:name=".MyFcmListenerService">
            <intent-filter>
                <action android:name="com.google.firebase.INSTANCE_ID_EVENT"/>
            </intent-filter>
        </service>-->

        <!--firebase-->
        <service
            android:exported="true"
            android:name=".firebase.MyFirebaseMessagingService">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT"/>
            </intent-filter>
        </service>

        <service
            android:exported="true"
            android:name=".firebase.MyFirebaseInstanceIDService">
            <intent-filter>
                <action android:name="com.google.firebase.INSTANCE_ID_EVENT"/>
            </intent-filter>
        </service>



        <!--<meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/logo" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/bill_back_color" />-->

        <meta-data
            android:name="io.fabric.ApiKey"
            android:value="6adbd2683865cdc979860fd7c49920d89bcaf4b6" />
    </application>


</manifest>